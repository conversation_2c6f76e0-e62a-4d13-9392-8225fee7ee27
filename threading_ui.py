"""
Threading User Interface

This module provides user interface components for configuring and monitoring
multithreaded GMX account creation.
"""

import sys
import time
import threading
from typing import Dict, Optional, Tuple, Any
from datetime import datetime

from proxy_manager import ProxyManager
from threaded_gmx_creator import ThreadProgress


class ThreadingConfigUI:
    """
    User interface for threading configuration and monitoring
    """
    
    def __init__(self):
        self.display_lock = threading.Lock()
        self.last_display_time = 0
        self.display_interval = 2.0  # Update every 2 seconds
    
    def get_threading_configuration(self, proxy_manager: ProxyManager) -> Dict[str, Any]:
        """
        Get threading configuration from user input
        
        Args:
            proxy_manager: Proxy manager to check available proxies
            
        Returns:
            Dictionary with threading configuration
        """
        print("\n" + "="*60)
        print("🚀 MULTITHREADED GMX ACCOUNT CREATION")
        print("="*60)
        
        # Check available proxies
        available_proxies = proxy_manager.get_available_proxies()
        proxy_count = len(available_proxies)
        
        print(f"📊 Available proxies: {proxy_count}")
        
        if proxy_count == 0:
            print("❌ No proxies available! Multithreading requires at least 1 proxy.")
            return {'use_threading': False, 'reason': 'no_proxies'}
        
        # Ask if user wants to use multithreading
        use_threading = self._ask_yes_no(
            "\n🔄 Do you want to use multithreading for faster account creation?",
            default=True
        )
        
        if not use_threading:
            return {'use_threading': False, 'reason': 'user_choice'}
        
        # Get thread count
        max_recommended = min(5, proxy_count)
        thread_count = self._get_thread_count(max_recommended, proxy_count)
        
        # Get additional configuration
        config = {
            'use_threading': True,
            'thread_count': thread_count,
            'max_proxies': proxy_count,
            'strategy': self._get_proxy_strategy(),
            'show_progress': self._ask_yes_no(
                "\n📈 Show real-time progress display?", 
                default=True
            ),
            'auto_cleanup': self._ask_yes_no(
                "\n🧹 Enable automatic resource cleanup?", 
                default=True
            )
        }
        
        # Display configuration summary
        self._display_configuration_summary(config)
        
        return config
    
    def _ask_yes_no(self, question: str, default: bool = True) -> bool:
        """Ask a yes/no question with default"""
        default_str = "Y/n" if default else "y/N"
        
        while True:
            try:
                response = input(f"{question} ({default_str}): ").strip().lower()
                
                if not response:
                    return default
                
                if response in ['y', 'yes', '1', 'true']:
                    return True
                elif response in ['n', 'no', '0', 'false']:
                    return False
                else:
                    print("❌ Please enter 'y' for yes or 'n' for no.")
                    
            except KeyboardInterrupt:
                print("\n\n⏹️ Operation cancelled by user.")
                sys.exit(0)
            except EOFError:
                return default
    
    def _get_thread_count(self, max_recommended: int, proxy_count: int) -> int:
        """Get thread count from user"""
        print(f"\n🧵 THREAD CONFIGURATION")
        print(f"   • Available proxies: {proxy_count}")
        print(f"   • Recommended threads: 3-{max_recommended}")
        print(f"   • Maximum safe threads: {min(5, proxy_count)}")
        
        while True:
            try:
                prompt = f"\nHow many concurrent threads? (1-{min(5, proxy_count)}, recommended: {max_recommended}): "
                response = input(prompt).strip()
                
                if not response:
                    return max_recommended
                
                thread_count = int(response)
                
                if thread_count < 1:
                    print("❌ Thread count must be at least 1.")
                    continue
                
                if thread_count > proxy_count:
                    print(f"❌ Thread count cannot exceed available proxies ({proxy_count}).")
                    continue
                
                if thread_count > 5:
                    print("❌ Maximum 5 threads allowed for system stability.")
                    continue
                
                if thread_count > max_recommended:
                    confirm = self._ask_yes_no(
                        f"⚠️  Using {thread_count} threads (above recommended {max_recommended}). Continue?",
                        default=False
                    )
                    if not confirm:
                        continue
                
                return thread_count
                
            except ValueError:
                print("❌ Please enter a valid number.")
            except KeyboardInterrupt:
                print("\n\n⏹️ Operation cancelled by user.")
                sys.exit(0)
    
    def _get_proxy_strategy(self) -> str:
        """Get proxy selection strategy from user"""
        print(f"\n🎯 PROXY STRATEGY")
        print("   1. Least Used (recommended) - Use proxies with lowest usage count")
        print("   2. Random - Randomly select from available proxies")
        print("   3. Sequential - Use proxies in order of last usage")
        
        while True:
            try:
                response = input("\nSelect proxy strategy (1-3, default: 1): ").strip()
                
                if not response or response == "1":
                    return "least_used"
                elif response == "2":
                    return "random"
                elif response == "3":
                    return "sequential"
                else:
                    print("❌ Please enter 1, 2, or 3.")
                    
            except KeyboardInterrupt:
                print("\n\n⏹️ Operation cancelled by user.")
                sys.exit(0)
    
    def _display_configuration_summary(self, config: Dict[str, Any]):
        """Display configuration summary"""
        print("\n" + "="*60)
        print("📋 THREADING CONFIGURATION SUMMARY")
        print("="*60)
        print(f"🧵 Threads: {config['thread_count']}")
        print(f"🌐 Available Proxies: {config['max_proxies']}")
        print(f"🎯 Proxy Strategy: {config['strategy'].replace('_', ' ').title()}")
        print(f"📈 Progress Display: {'Enabled' if config['show_progress'] else 'Disabled'}")
        print(f"🧹 Auto Cleanup: {'Enabled' if config['auto_cleanup'] else 'Disabled'}")
        print("="*60)
        
        # Wait for user confirmation
        input("\n✅ Press Enter to start multithreaded account creation...")
    
    def display_startup_info(self, config: Dict[str, Any], total_accounts: int):
        """Display startup information"""
        print("\n" + "🚀" + "="*58 + "🚀")
        print("    STARTING MULTITHREADED GMX ACCOUNT CREATION")
        print("🚀" + "="*58 + "🚀")
        print(f"📊 Total Accounts: {total_accounts}")
        print(f"🧵 Concurrent Threads: {config['thread_count']}")
        print(f"⚡ Accounts per Thread: ~{total_accounts // config['thread_count']}")
        print(f"🕒 Estimated Start Time: {datetime.now().strftime('%H:%M:%S')}")
        print("="*62)
        print("🔄 Initializing threads and allocating resources...")
        print("="*62)
    
    def display_thread_progress(self, progress_data: Dict[int, ThreadProgress], 
                              clear_screen: bool = True):
        """
        Display real-time progress for all threads
        
        Args:
            progress_data: Dictionary of thread progress data
            clear_screen: Whether to clear screen before display
        """
        with self.display_lock:
            current_time = time.time()
            
            # Throttle display updates
            if current_time - self.last_display_time < self.display_interval:
                return
            
            self.last_display_time = current_time
            
            if clear_screen:
                # Clear screen (works on Windows and Unix)
                import os
                os.system('cls' if os.name == 'nt' else 'clear')
            
            print("\n" + "🔄" + "="*78 + "🔄")
            print(f"    GMX MULTITHREADED CREATION - {datetime.now().strftime('%H:%M:%S')}")
            print("🔄" + "="*78 + "🔄")
            
            if not progress_data:
                print("⏳ Initializing threads...")
                print("="*82)
                return
            
            # Calculate totals
            total_created = sum(p.accounts_created for p in progress_data.values())
            total_failed = sum(p.accounts_failed for p in progress_data.values())
            total_target = sum(p.total_accounts for p in progress_data.values())
            
            # Display individual thread progress
            for thread_id in sorted(progress_data.keys()):
                progress = progress_data[thread_id]
                self._display_single_thread_progress(progress)
            
            # Display summary
            print("─" * 82)
            self._display_progress_summary(total_created, total_failed, total_target)
            print("🔄" + "="*78 + "🔄")
    
    def _display_single_thread_progress(self, progress: ThreadProgress):
        """Display progress for a single thread"""
        # Status icons
        status_icons = {
            'running': '🔄',
            'completed': '✅',
            'failed': '❌',
            'stopped': '⏹️'
        }
        
        icon = status_icons.get(progress.status, '❓')
        
        # Calculate progress percentage
        total_attempted = progress.accounts_created + progress.accounts_failed
        if progress.total_accounts > 0:
            progress_pct = (total_attempted / progress.total_accounts) * 100
        else:
            progress_pct = 0
        
        # Calculate elapsed time
        elapsed = (datetime.now() - progress.start_time).total_seconds()
        elapsed_str = f"{int(elapsed//60):02d}:{int(elapsed%60):02d}"
        
        # Format proxy info
        proxy_info = f"Proxy: {progress.current_proxy[:12]}..." if progress.current_proxy else "No proxy"
        
        # Create progress bar
        bar_width = 20
        filled = int((progress_pct / 100) * bar_width)
        bar = "█" * filled + "░" * (bar_width - filled)
        
        print(f"{icon} Thread-{progress.thread_id:2d} │ {bar} │ "
              f"{progress.accounts_created:2d}/{progress.total_accounts:2d} │ "
              f"❌{progress.accounts_failed:2d} │ {elapsed_str} │ {proxy_info}")
        
        # Show current step if running
        if progress.status == 'running' and progress.current_step:
            step_display = progress.current_step[:60] + "..." if len(progress.current_step) > 60 else progress.current_step
            print(f"    └─ {step_display}")
        
        # Show error if failed
        if progress.error_message:
            error_display = progress.error_message[:60] + "..." if len(progress.error_message) > 60 else progress.error_message
            print(f"    └─ ❌ {error_display}")
    
    def _display_progress_summary(self, total_created: int, total_failed: int, total_target: int):
        """Display overall progress summary"""
        total_attempted = total_created + total_failed
        
        if total_attempted > 0:
            success_rate = (total_created / total_attempted) * 100
        else:
            success_rate = 0
        
        if total_target > 0:
            completion_rate = (total_attempted / total_target) * 100
        else:
            completion_rate = 0
        
        print(f"📊 OVERALL: {total_created}/{total_target} created │ "
              f"❌ {total_failed} failed │ "
              f"✅ {success_rate:.1f}% success │ "
              f"📈 {completion_rate:.1f}% complete")
    
    def display_completion_summary(self, results: Dict[str, Any]):
        """Display final completion summary"""
        print("\n" + "🎉" + "="*78 + "🎉")
        print("    MULTITHREADED GMX CREATION COMPLETED")
        print("🎉" + "="*78 + "🎉")
        
        print(f"📊 Total Requested: {results['total_accounts_requested']}")
        print(f"✅ Successfully Created: {results['total_successful']}")
        print(f"❌ Failed: {results['total_failed']}")
        print(f"🧵 Threads Used: {results['threads_used']}")
        print(f"📈 Success Rate: {results['success_rate']:.1f}%")
        print(f"📋 Completion Rate: {results['completion_rate']:.1f}%")
        
        print("\n📋 THREAD BREAKDOWN:")
        print("─" * 82)
        
        for thread_id, thread_result in results['thread_results'].items():
            status_icon = '✅' if thread_result['status'] == 'completed' else '❌'
            duration_str = f"{int(thread_result['duration']//60):02d}:{int(thread_result['duration']%60):02d}"
            
            print(f"{status_icon} Thread-{thread_id:2d} │ "
                  f"✅ {thread_result['accounts_created']:2d} │ "
                  f"❌ {thread_result['accounts_failed']:2d} │ "
                  f"⏱️  {duration_str} │ "
                  f"🌐 {thread_result['proxy_used'] or 'No proxy'}")
        
        print("🎉" + "="*78 + "🎉")
        
        if results['total_successful'] > 0:
            print(f"🎊 Congratulations! {results['total_successful']} GMX accounts created successfully!")
        
        if results['total_failed'] > 0:
            print(f"⚠️  {results['total_failed']} accounts failed. Check logs for details.")
        
        print("🎉" + "="*78 + "🎉")
    
    def display_error_message(self, error: str, context: str = ""):
        """Display error message with context"""
        print("\n" + "❌" + "="*78 + "❌")
        print("    MULTITHREADING ERROR")
        if context:
            print(f"    Context: {context}")
        print("❌" + "="*78 + "❌")
        print(f"🚨 Error: {error}")
        print("❌" + "="*78 + "❌")
    
    def ask_fallback_to_single_thread(self) -> bool:
        """Ask user if they want to fallback to single-threaded mode"""
        print("\n⚠️  Multithreading is not available or failed to initialize.")
        return self._ask_yes_no(
            "🔄 Would you like to continue with single-threaded mode?",
            default=True
        )
