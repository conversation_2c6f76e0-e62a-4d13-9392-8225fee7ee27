"""
Threaded GMX Account Creator

This module implements multithreading support for GMX account creation,
allowing multiple concurrent instances with proxy isolation and resource management.
"""

import threading
import time
import logging
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime

from proxy_manager import ProxyManager


@dataclass
class ThreadProgress:
    """Track progress for individual threads"""
    thread_id: int
    thread_name: str
    current_step: str
    accounts_created: int
    accounts_failed: int
    total_accounts: int
    status: str  # 'running', 'completed', 'failed', 'stopped'
    start_time: datetime
    last_update: datetime
    current_proxy: Optional[str] = None
    error_message: Optional[str] = None


class ThreadSafeLogger:
    """Thread-safe logger with thread-specific prefixes"""
    
    def __init__(self, base_logger_name: str = "ThreadedGMXCreator"):
        self.base_logger = logging.getLogger(base_logger_name)
        self._lock = threading.Lock()
    
    def get_thread_logger(self, thread_id: int, thread_name: str) -> logging.Logger:
        """Get a logger with thread-specific formatting"""
        logger_name = f"{self.base_logger.name}.Thread-{thread_id}"
        thread_logger = logging.getLogger(logger_name)
        
        # Create custom formatter with thread prefix
        if not thread_logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                f'%(asctime)s - [Thread-{thread_id}] - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            handler.setFormatter(formatter)
            thread_logger.addHandler(handler)
            thread_logger.setLevel(self.base_logger.level)
        
        return thread_logger
    
    def log_coordinated(self, level: str, message: str, thread_id: int = None):
        """Log a message in a thread-safe manner"""
        with self._lock:
            if thread_id:
                prefix = f"[Thread-{thread_id}]"
            else:
                prefix = "[Main]"
            
            log_method = getattr(self.base_logger, level.lower())
            log_method(f"{prefix} {message}")


class ThreadedAccountCreator:
    """
    Manages multiple threaded GMX account creation instances
    """
    
    def __init__(self, proxy_manager: ProxyManager, max_threads: int = 5):
        """
        Initialize the threaded account creator
        
        Args:
            proxy_manager: Thread-safe proxy manager instance
            max_threads: Maximum number of concurrent threads
        """
        self.proxy_manager = proxy_manager
        self.max_threads = min(max_threads, 5)  # Cap at 5 for safety
        
        # Thread management
        self.thread_pool: Optional[ThreadPoolExecutor] = None
        self.progress_tracker: Dict[int, ThreadProgress] = {}
        self.progress_lock = threading.Lock()
        
        # Logging
        self.thread_logger = ThreadSafeLogger()
        self.logger = self.thread_logger.base_logger
        
        # Control flags
        self.stop_requested = threading.Event()
        self.progress_display_thread: Optional[threading.Thread] = None
        
        # Results tracking
        self.results_queue = queue.Queue()
        self.total_successful = 0
        self.total_failed = 0
        
        self.logger.info(f"ThreadedAccountCreator initialized with max {max_threads} threads")
    
    def create_accounts_threaded(self, 
                               total_accounts: int,
                               account_creator_func: Callable,
                               thread_count: Optional[int] = None) -> Dict[str, Any]:
        """
        Create GMX accounts using multiple threads
        
        Args:
            total_accounts: Total number of accounts to create
            account_creator_func: Function to create a single account
            thread_count: Number of threads to use (None for auto-detect)
            
        Returns:
            Dictionary with creation results and statistics
        """
        if thread_count is None:
            thread_count = min(self.max_threads, max(1, total_accounts // 2))
        
        thread_count = min(thread_count, self.max_threads, total_accounts)
        
        self.logger.info(f"Starting threaded account creation: {total_accounts} accounts, {thread_count} threads")
        
        # Calculate accounts per thread
        accounts_per_thread = total_accounts // thread_count
        remaining_accounts = total_accounts % thread_count
        
        # Reset tracking
        self.progress_tracker.clear()
        self.stop_requested.clear()
        self.total_successful = 0
        self.total_failed = 0
        
        # Start progress display thread
        self._start_progress_display()
        
        try:
            # Create thread pool and submit tasks
            with ThreadPoolExecutor(max_workers=thread_count, thread_name_prefix="GMX-Worker") as executor:
                self.thread_pool = executor
                futures = []
                
                for thread_id in range(thread_count):
                    # Calculate accounts for this thread
                    thread_accounts = accounts_per_thread
                    if thread_id < remaining_accounts:
                        thread_accounts += 1
                    
                    if thread_accounts > 0:
                        # Submit thread task
                        future = executor.submit(
                            self._thread_worker,
                            thread_id,
                            thread_accounts,
                            account_creator_func
                        )
                        futures.append((thread_id, future))
                
                # Wait for all threads to complete
                self._wait_for_completion(futures)
                
        except KeyboardInterrupt:
            self.logger.warning("Keyboard interrupt received, stopping threads...")
            self.stop_requested.set()
        except Exception as e:
            self.logger.error(f"Error in threaded execution: {str(e)}")
            self.stop_requested.set()
        finally:
            # Stop progress display
            self._stop_progress_display()
            self.thread_pool = None
        
        # Compile final results
        return self._compile_results()
    
    def _thread_worker(self, thread_id: int, accounts_to_create: int, account_creator_func: Callable):
        """
        Worker function for individual threads
        
        Args:
            thread_id: Unique thread identifier
            accounts_to_create: Number of accounts this thread should create
            account_creator_func: Function to create a single account
        """
        thread_name = f"GMX-Worker-{thread_id}"
        thread_logger = self.thread_logger.get_thread_logger(thread_id, thread_name)
        
        # Initialize progress tracking
        progress = ThreadProgress(
            thread_id=thread_id,
            thread_name=thread_name,
            current_step="Initializing",
            accounts_created=0,
            accounts_failed=0,
            total_accounts=accounts_to_create,
            status="running",
            start_time=datetime.now(),
            last_update=datetime.now()
        )
        
        with self.progress_lock:
            self.progress_tracker[thread_id] = progress
        
        thread_logger.info(f"Thread started - will create {accounts_to_create} accounts")
        
        try:
            # Reserve a proxy for this thread
            selected_proxy = self.proxy_manager.rotate_proxy(strategy="least_used", reserve=True)
            if not selected_proxy:
                thread_logger.error("No available proxy found for thread")
                progress.status = "failed"
                progress.error_message = "No available proxy"
                return
            
            progress.current_proxy = selected_proxy['id']
            thread_logger.info(f"Reserved proxy: {selected_proxy['id']}")
            
            # Create accounts
            for account_num in range(accounts_to_create):
                if self.stop_requested.is_set():
                    thread_logger.info("Stop requested, terminating thread")
                    break
                
                # Update progress
                progress.current_step = f"Creating account {account_num + 1}/{accounts_to_create}"
                progress.last_update = datetime.now()
                
                thread_logger.info(f"Creating account {account_num + 1}/{accounts_to_create}")
                
                try:
                    # Create single account
                    success = account_creator_func(thread_id, thread_logger)
                    
                    if success:
                        progress.accounts_created += 1
                        self.total_successful += 1
                        thread_logger.info(f"Account {account_num + 1} created successfully")
                    else:
                        progress.accounts_failed += 1
                        self.total_failed += 1
                        thread_logger.warning(f"Account {account_num + 1} creation failed")
                    
                    # Small delay between accounts
                    if not self.stop_requested.is_set():
                        time.sleep(2)
                        
                except Exception as e:
                    progress.accounts_failed += 1
                    self.total_failed += 1
                    thread_logger.error(f"Error creating account {account_num + 1}: {str(e)}")
            
            # Mark proxy as used
            if selected_proxy:
                success_rate = progress.accounts_created / max(1, progress.accounts_created + progress.accounts_failed)
                self.proxy_manager.mark_proxy_used(selected_proxy['id'], success=success_rate > 0.5)
            
            progress.status = "completed"
            thread_logger.info(f"Thread completed - Created: {progress.accounts_created}, Failed: {progress.accounts_failed}")
            
        except Exception as e:
            progress.status = "failed"
            progress.error_message = str(e)
            thread_logger.error(f"Thread failed with error: {str(e)}")
        finally:
            progress.last_update = datetime.now()
            # Release proxy reservation if still held
            if progress.current_proxy:
                self.proxy_manager.release_proxy_reservation(progress.current_proxy)

    def _wait_for_completion(self, futures: List[tuple]):
        """Wait for all thread futures to complete"""
        for thread_id, future in futures:
            try:
                future.result(timeout=3600)  # 1 hour timeout per thread
            except Exception as e:
                self.logger.error(f"Thread {thread_id} failed: {str(e)}")
                with self.progress_lock:
                    if thread_id in self.progress_tracker:
                        self.progress_tracker[thread_id].status = "failed"
                        self.progress_tracker[thread_id].error_message = str(e)

    def _start_progress_display(self):
        """Start the progress display thread"""
        self.progress_display_thread = threading.Thread(
            target=self._progress_display_worker,
            name="ProgressDisplay",
            daemon=True
        )
        self.progress_display_thread.start()

    def _stop_progress_display(self):
        """Stop the progress display thread"""
        if self.progress_display_thread and self.progress_display_thread.is_alive():
            # Progress display will stop when stop_requested is set
            self.stop_requested.set()
            self.progress_display_thread.join(timeout=5)

    def _progress_display_worker(self):
        """Worker function for progress display thread"""
        while not self.stop_requested.is_set():
            try:
                self._display_progress()
                time.sleep(5)  # Update every 5 seconds
            except Exception as e:
                self.logger.error(f"Error in progress display: {str(e)}")
                break

    def _display_progress(self):
        """Display current progress for all threads"""
        with self.progress_lock:
            if not self.progress_tracker:
                return

            print("\n" + "="*80)
            print(f"GMX Account Creation Progress - {datetime.now().strftime('%H:%M:%S')}")
            print("="*80)

            total_created = 0
            total_failed = 0
            total_target = 0

            for thread_id, progress in self.progress_tracker.items():
                status_icon = {
                    'running': '🔄',
                    'completed': '✅',
                    'failed': '❌',
                    'stopped': '⏹️'
                }.get(progress.status, '❓')

                elapsed = (datetime.now() - progress.start_time).total_seconds()
                elapsed_str = f"{int(elapsed//60):02d}:{int(elapsed%60):02d}"

                proxy_info = f"Proxy: {progress.current_proxy}" if progress.current_proxy else "No proxy"

                print(f"{status_icon} Thread-{thread_id}: {progress.current_step}")
                print(f"   Created: {progress.accounts_created}/{progress.total_accounts} | "
                      f"Failed: {progress.accounts_failed} | "
                      f"Time: {elapsed_str} | {proxy_info}")

                if progress.error_message:
                    print(f"   Error: {progress.error_message}")

                total_created += progress.accounts_created
                total_failed += progress.accounts_failed
                total_target += progress.total_accounts

            print("-"*80)
            print(f"Overall: {total_created}/{total_target} created, {total_failed} failed")
            success_rate = (total_created / max(1, total_created + total_failed)) * 100
            print(f"Success Rate: {success_rate:.1f}%")
            print("="*80)

    def _compile_results(self) -> Dict[str, Any]:
        """Compile final results from all threads"""
        with self.progress_lock:
            results = {
                'total_accounts_requested': sum(p.total_accounts for p in self.progress_tracker.values()),
                'total_successful': sum(p.accounts_created for p in self.progress_tracker.values()),
                'total_failed': sum(p.accounts_failed for p in self.progress_tracker.values()),
                'threads_used': len(self.progress_tracker),
                'thread_results': {}
            }

            for thread_id, progress in self.progress_tracker.items():
                results['thread_results'][thread_id] = {
                    'accounts_created': progress.accounts_created,
                    'accounts_failed': progress.accounts_failed,
                    'status': progress.status,
                    'proxy_used': progress.current_proxy,
                    'error_message': progress.error_message,
                    'duration': (progress.last_update - progress.start_time).total_seconds()
                }

            # Calculate overall statistics
            total_attempted = results['total_successful'] + results['total_failed']
            results['success_rate'] = (results['total_successful'] / max(1, total_attempted)) * 100
            results['completion_rate'] = (total_attempted / max(1, results['total_accounts_requested'])) * 100

            return results

    def get_current_progress(self) -> Dict[int, ThreadProgress]:
        """Get current progress for all threads (thread-safe)"""
        with self.progress_lock:
            return {tid: progress for tid, progress in self.progress_tracker.items()}

    def stop_all_threads(self):
        """Request all threads to stop"""
        self.logger.info("Requesting all threads to stop...")
        self.stop_requested.set()

        if self.thread_pool:
            # Note: ThreadPoolExecutor doesn't have a direct way to cancel running futures
            # The threads will check stop_requested flag and terminate gracefully
            pass

    def is_running(self) -> bool:
        """Check if any threads are currently running"""
        with self.progress_lock:
            return any(p.status == 'running' for p in self.progress_tracker.values())


def create_threaded_gmx_accounts(total_accounts: int,
                               thread_count: int,
                               proxy_manager: ProxyManager,
                               account_creator_func: Callable) -> Dict[str, Any]:
    """
    Convenience function to create GMX accounts using threading

    Args:
        total_accounts: Total number of accounts to create
        thread_count: Number of concurrent threads
        proxy_manager: Thread-safe proxy manager
        account_creator_func: Function to create a single account

    Returns:
        Dictionary with creation results
    """
    creator = ThreadedAccountCreator(proxy_manager, max_threads=thread_count)
    return creator.create_accounts_threaded(total_accounts, account_creator_func, thread_count)
