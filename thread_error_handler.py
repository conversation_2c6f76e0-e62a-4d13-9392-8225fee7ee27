"""
Thread Error Handler and Recovery System

This module provides robust error handling, isolation, and recovery mechanisms
for multithreaded GMX account creation to ensure system stability.
"""

import threading
import logging
import time
import traceback
import signal
import sys
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

from thread_resource_manager import ThreadResourceManager


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"           # Minor errors, continue operation
    MEDIUM = "medium"     # Moderate errors, retry with backoff
    HIGH = "high"         # Serious errors, terminate thread
    CRITICAL = "critical" # System-wide errors, terminate all


@dataclass
class ThreadError:
    """Container for thread error information"""
    thread_id: int
    error_type: str
    error_message: str
    severity: ErrorSeverity
    timestamp: datetime
    stack_trace: Optional[str] = None
    retry_count: int = 0
    context: Optional[Dict[str, Any]] = None


class ThreadErrorHandler:
    """
    Handles errors, isolation, and recovery for multithreaded operations
    """
    
    def __init__(self, resource_manager: ThreadResourceManager):
        """
        Initialize the error handler
        
        Args:
            resource_manager: Thread resource manager instance
        """
        self.resource_manager = resource_manager
        self.logger = logging.getLogger(f"{__name__}.ThreadErrorHandler")
        
        # Error tracking
        self._error_lock = threading.RLock()
        self._thread_errors: Dict[int, List[ThreadError]] = {}
        self._error_counts: Dict[int, int] = {}
        self._last_error_time: Dict[int, datetime] = {}
        
        # Recovery settings
        self.max_retries_per_thread = 3
        self.max_errors_per_thread = 10
        self.error_cooldown_seconds = 30
        self.critical_error_threshold = 5  # Critical errors before system shutdown
        
        # System state
        self.system_shutdown_requested = threading.Event()
        self.critical_error_count = 0
        
        # Recovery strategies
        self.recovery_strategies = {
            'browser_error': self._recover_browser_error,
            'proxy_error': self._recover_proxy_error,
            'network_error': self._recover_network_error,
            'timeout_error': self._recover_timeout_error,
            'memory_error': self._recover_memory_error,
            'unknown_error': self._recover_unknown_error
        }
        
        # Setup signal handlers for graceful shutdown
        self._setup_signal_handlers()
        
        self.logger.info("ThreadErrorHandler initialized")
    
    def handle_thread_error(self, thread_id: int, error: Exception, 
                          context: Optional[Dict[str, Any]] = None) -> bool:
        """
        Handle an error from a specific thread
        
        Args:
            thread_id: Thread identifier
            error: Exception that occurred
            context: Additional context information
            
        Returns:
            True if thread should continue, False if thread should terminate
        """
        with self._error_lock:
            # Classify error
            error_info = self._classify_error(thread_id, error, context)
            
            # Record error
            self._record_error(error_info)
            
            # Log error
            self._log_error(error_info)
            
            # Check if thread should be terminated
            if self._should_terminate_thread(thread_id, error_info):
                self.logger.warning(f"Thread {thread_id}: Terminating due to error severity/frequency")
                self._cleanup_failed_thread(thread_id)
                return False
            
            # Check for critical system errors
            if error_info.severity == ErrorSeverity.CRITICAL:
                self.critical_error_count += 1
                if self.critical_error_count >= self.critical_error_threshold:
                    self.logger.critical("Critical error threshold reached, initiating system shutdown")
                    self._initiate_system_shutdown()
                    return False
            
            # Attempt recovery
            recovery_success = self._attempt_recovery(error_info)
            
            if recovery_success:
                self.logger.info(f"Thread {thread_id}: Recovery successful, continuing operation")
                return True
            else:
                self.logger.warning(f"Thread {thread_id}: Recovery failed, terminating thread")
                self._cleanup_failed_thread(thread_id)
                return False
    
    def _classify_error(self, thread_id: int, error: Exception, 
                       context: Optional[Dict[str, Any]] = None) -> ThreadError:
        """Classify error and determine severity"""
        error_type = type(error).__name__
        error_message = str(error)
        stack_trace = traceback.format_exc()
        
        # Determine error category and severity
        if "browser" in error_message.lower() or "selenium" in error_message.lower():
            category = "browser_error"
            severity = ErrorSeverity.MEDIUM
        elif "proxy" in error_message.lower() or "connection" in error_message.lower():
            category = "proxy_error"
            severity = ErrorSeverity.MEDIUM
        elif "timeout" in error_message.lower():
            category = "timeout_error"
            severity = ErrorSeverity.LOW
        elif "memory" in error_message.lower() or isinstance(error, MemoryError):
            category = "memory_error"
            severity = ErrorSeverity.HIGH
        elif "network" in error_message.lower() or "dns" in error_message.lower():
            category = "network_error"
            severity = ErrorSeverity.MEDIUM
        else:
            category = "unknown_error"
            severity = ErrorSeverity.MEDIUM
        
        # Escalate severity based on error frequency
        error_count = self._error_counts.get(thread_id, 0)
        if error_count > 5:
            if severity == ErrorSeverity.LOW:
                severity = ErrorSeverity.MEDIUM
            elif severity == ErrorSeverity.MEDIUM:
                severity = ErrorSeverity.HIGH
        
        return ThreadError(
            thread_id=thread_id,
            error_type=category,
            error_message=error_message,
            severity=severity,
            timestamp=datetime.now(),
            stack_trace=stack_trace,
            context=context or {}
        )
    
    def _record_error(self, error_info: ThreadError):
        """Record error in tracking system"""
        thread_id = error_info.thread_id
        
        if thread_id not in self._thread_errors:
            self._thread_errors[thread_id] = []
        
        self._thread_errors[thread_id].append(error_info)
        self._error_counts[thread_id] = self._error_counts.get(thread_id, 0) + 1
        self._last_error_time[thread_id] = error_info.timestamp
        
        # Limit error history to prevent memory growth
        if len(self._thread_errors[thread_id]) > 20:
            self._thread_errors[thread_id] = self._thread_errors[thread_id][-20:]
    
    def _log_error(self, error_info: ThreadError):
        """Log error with appropriate level"""
        log_message = (f"Thread {error_info.thread_id}: {error_info.error_type} - "
                      f"{error_info.error_message}")
        
        if error_info.severity == ErrorSeverity.LOW:
            self.logger.warning(log_message)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            self.logger.error(log_message)
        elif error_info.severity == ErrorSeverity.HIGH:
            self.logger.error(f"HIGH SEVERITY: {log_message}")
        else:  # CRITICAL
            self.logger.critical(f"CRITICAL ERROR: {log_message}")
        
        # Log stack trace for medium+ severity
        if error_info.severity.value in ['medium', 'high', 'critical'] and error_info.stack_trace:
            self.logger.debug(f"Thread {error_info.thread_id} stack trace:\n{error_info.stack_trace}")
    
    def _should_terminate_thread(self, thread_id: int, error_info: ThreadError) -> bool:
        """Determine if thread should be terminated"""
        # Always terminate on high/critical severity
        if error_info.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            return True
        
        # Terminate if too many errors
        if self._error_counts.get(thread_id, 0) > self.max_errors_per_thread:
            return True
        
        # Terminate if errors are too frequent
        last_error_time = self._last_error_time.get(thread_id)
        if last_error_time:
            time_since_last = (datetime.now() - last_error_time).total_seconds()
            if time_since_last < self.error_cooldown_seconds and self._error_counts.get(thread_id, 0) > 3:
                return True
        
        return False
    
    def _attempt_recovery(self, error_info: ThreadError) -> bool:
        """Attempt to recover from error"""
        recovery_func = self.recovery_strategies.get(error_info.error_type, self._recover_unknown_error)
        
        try:
            self.logger.info(f"Thread {error_info.thread_id}: Attempting recovery for {error_info.error_type}")
            
            # Add backoff delay based on retry count
            if error_info.retry_count > 0:
                delay = min(30, 2 ** error_info.retry_count)  # Exponential backoff, max 30s
                self.logger.info(f"Thread {error_info.thread_id}: Recovery backoff delay: {delay}s")
                time.sleep(delay)
            
            success = recovery_func(error_info)
            
            if success:
                error_info.retry_count += 1
                self.logger.info(f"Thread {error_info.thread_id}: Recovery attempt {error_info.retry_count} successful")
            else:
                self.logger.warning(f"Thread {error_info.thread_id}: Recovery attempt failed")
            
            return success
            
        except Exception as recovery_error:
            self.logger.error(f"Thread {error_info.thread_id}: Recovery failed with error: {str(recovery_error)}")
            return False
    
    def _recover_browser_error(self, error_info: ThreadError) -> bool:
        """Recover from browser-related errors"""
        thread_id = error_info.thread_id
        
        try:
            # Force cleanup of browser resources
            self.resource_manager.cleanup_thread_resources(thread_id, force=True)
            
            # Wait for cleanup to complete
            time.sleep(5)
            
            # Resources will be reallocated when thread restarts
            return True
            
        except Exception as e:
            self.logger.error(f"Thread {thread_id}: Browser recovery failed: {str(e)}")
            return False
    
    def _recover_proxy_error(self, error_info: ThreadError) -> bool:
        """Recover from proxy-related errors"""
        thread_id = error_info.thread_id
        
        try:
            # Get current thread resources
            resources = self.resource_manager.get_thread_resources(thread_id)
            if not resources:
                return False
            
            # Release current proxy and get a new one
            if resources.proxy_id:
                self.resource_manager.proxy_manager.release_proxy_reservation(resources.proxy_id)
            
            # Try to get a new proxy
            new_proxy = self.resource_manager.proxy_manager.rotate_proxy(strategy="random", reserve=True)
            if new_proxy:
                resources.proxy_config = new_proxy
                resources.proxy_id = new_proxy['id']
                self.logger.info(f"Thread {thread_id}: Switched to new proxy: {new_proxy['id']}")
                return True
            else:
                self.logger.warning(f"Thread {thread_id}: No alternative proxy available")
                return False
                
        except Exception as e:
            self.logger.error(f"Thread {thread_id}: Proxy recovery failed: {str(e)}")
            return False
    
    def _recover_network_error(self, error_info: ThreadError) -> bool:
        """Recover from network-related errors"""
        # Wait for network to stabilize
        time.sleep(10)
        return True
    
    def _recover_timeout_error(self, error_info: ThreadError) -> bool:
        """Recover from timeout errors"""
        # Simple retry with delay
        time.sleep(5)
        return True
    
    def _recover_memory_error(self, error_info: ThreadError) -> bool:
        """Recover from memory errors"""
        thread_id = error_info.thread_id
        
        try:
            # Force garbage collection
            import gc
            gc.collect()
            
            # Cleanup thread resources to free memory
            self.resource_manager.cleanup_thread_resources(thread_id, force=True)
            
            # Wait for memory to be freed
            time.sleep(10)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Thread {thread_id}: Memory recovery failed: {str(e)}")
            return False
    
    def _recover_unknown_error(self, error_info: ThreadError) -> bool:
        """Generic recovery for unknown errors"""
        # Conservative approach: cleanup and retry
        thread_id = error_info.thread_id
        
        try:
            self.resource_manager.cleanup_thread_resources(thread_id, force=True)
            time.sleep(5)
            return True
        except Exception:
            return False
    
    def _cleanup_failed_thread(self, thread_id: int):
        """Cleanup resources for a failed thread"""
        try:
            self.resource_manager.deallocate_thread_resources(thread_id)
            self.logger.info(f"Thread {thread_id}: Cleanup completed after failure")
        except Exception as e:
            self.logger.error(f"Thread {thread_id}: Cleanup failed: {str(e)}")
    
    def _initiate_system_shutdown(self):
        """Initiate graceful system shutdown"""
        self.logger.critical("Initiating system shutdown due to critical errors")
        self.system_shutdown_requested.set()
        
        # Cleanup all resources
        try:
            self.resource_manager.cleanup_all_resources()
        except Exception as e:
            self.logger.error(f"Error during system shutdown cleanup: {str(e)}")
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, initiating graceful shutdown")
            self._initiate_system_shutdown()
        
        try:
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
        except Exception as e:
            self.logger.warning(f"Could not setup signal handlers: {str(e)}")
    
    def is_system_shutdown_requested(self) -> bool:
        """Check if system shutdown has been requested"""
        return self.system_shutdown_requested.is_set()
    
    def get_thread_error_summary(self, thread_id: int) -> Dict[str, Any]:
        """Get error summary for a specific thread"""
        with self._error_lock:
            errors = self._thread_errors.get(thread_id, [])
            error_count = self._error_counts.get(thread_id, 0)
            last_error = self._last_error_time.get(thread_id)
            
            return {
                'thread_id': thread_id,
                'total_errors': error_count,
                'recent_errors': len([e for e in errors if (datetime.now() - e.timestamp).total_seconds() < 300]),
                'last_error_time': last_error.isoformat() if last_error else None,
                'error_types': list(set(e.error_type for e in errors)),
                'max_severity': max([e.severity.value for e in errors], default='none')
            }
    
    def get_system_error_summary(self) -> Dict[str, Any]:
        """Get overall system error summary"""
        with self._error_lock:
            return {
                'total_threads_with_errors': len(self._thread_errors),
                'total_error_count': sum(self._error_counts.values()),
                'critical_error_count': self.critical_error_count,
                'system_shutdown_requested': self.system_shutdown_requested.is_set(),
                'thread_summaries': {
                    tid: self.get_thread_error_summary(tid) 
                    for tid in self._thread_errors.keys()
                }
            }
