"""
Test Suite for Multithreaded GMX Account Creation

This module provides comprehensive tests for the threading implementation,
including proxy isolation, resource management, error handling, and system stability.
"""

import unittest
import threading
import time
import tempfile
import os
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Import the threading modules
from proxy_manager import Proxy<PERSON><PERSON><PERSON>
from threaded_gmx_creator import ThreadedAccountC<PERSON>, ThreadProgress
from thread_resource_manager import ThreadResourceManager, ThreadResources
from thread_error_handler import Thread<PERSON><PERSON>r<PERSON><PERSON><PERSON>, ErrorSeverity
from threading_ui import ThreadingConfigUI


class TestProxyManagerThreadSafety(unittest.TestCase):
    """Test thread safety of ProxyManager"""
    
    def setUp(self):
        """Set up test environment"""
        # Create temporary proxy file
        self.temp_dir = tempfile.mkdtemp()
        self.proxy_file = os.path.join(self.temp_dir, "test_proxy.txt")
        self.usage_file = os.path.join(self.temp_dir, "test_usage.json")
        
        # Create test proxy file
        with open(self.proxy_file, 'w') as f:
            f.write("proxy1:8080:user1:pass1\n")
            f.write("proxy2:8080:user2:pass2\n")
            f.write("proxy3:8080:user3:pass3\n")
        
        self.proxy_manager = ProxyManager(
            proxy_file=self.proxy_file,
            usage_file=self.usage_file,
            cooldown_minutes=1
        )
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_concurrent_proxy_selection(self):
        """Test concurrent proxy selection doesn't cause conflicts"""
        selected_proxies = []
        errors = []
        
        def select_proxy(thread_id):
            try:
                proxy = self.proxy_manager.select_next_proxy(strategy="least_used", reserve=True)
                if proxy:
                    selected_proxies.append((thread_id, proxy['id']))
                    time.sleep(0.1)  # Simulate work
                    self.proxy_manager.mark_proxy_used(proxy['id'], success=True)
            except Exception as e:
                errors.append((thread_id, str(e)))
        
        # Start multiple threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=select_proxy, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        # Verify results
        self.assertEqual(len(errors), 0, f"Errors occurred: {errors}")
        self.assertEqual(len(selected_proxies), 3, "Not all threads got proxies")
        
        # Verify no duplicate proxies were selected
        proxy_ids = [proxy_id for _, proxy_id in selected_proxies]
        self.assertEqual(len(proxy_ids), len(set(proxy_ids)), "Duplicate proxies selected")
    
    def test_proxy_reservation_system(self):
        """Test proxy reservation prevents conflicts"""
        # Reserve a proxy
        proxy1 = self.proxy_manager.select_next_proxy(reserve=True)
        self.assertIsNotNone(proxy1)
        
        # Try to get another proxy - should get different one
        proxy2 = self.proxy_manager.select_next_proxy(reserve=True)
        self.assertIsNotNone(proxy2)
        self.assertNotEqual(proxy1['id'], proxy2['id'])
        
        # Release first proxy
        self.proxy_manager.release_proxy_reservation(proxy1['id'])
        
        # Should be able to reserve it again
        proxy3 = self.proxy_manager.select_next_proxy(reserve=True)
        self.assertIsNotNone(proxy3)


class TestThreadResourceManager(unittest.TestCase):
    """Test thread resource management"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.proxy_file = os.path.join(self.temp_dir, "test_proxy.txt")
        
        with open(self.proxy_file, 'w') as f:
            f.write("proxy1:8080:user1:pass1\n")
            f.write("proxy2:8080:user2:pass2\n")
        
        self.proxy_manager = ProxyManager(proxy_file=self.proxy_file)
        self.resource_manager = ThreadResourceManager(self.proxy_manager)
    
    def tearDown(self):
        """Clean up test environment"""
        self.resource_manager.cleanup_all_resources()
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_resource_allocation(self):
        """Test thread resource allocation"""
        # Allocate resources for thread
        resources = self.resource_manager.allocate_thread_resources(1, "test-thread-1")
        
        self.assertIsNotNone(resources)
        self.assertEqual(resources.thread_id, 1)
        self.assertEqual(resources.thread_name, "test-thread-1")
        self.assertIsNotNone(resources.proxy_config)
        self.assertTrue(resources.is_active)
    
    def test_resource_isolation(self):
        """Test that thread resources are properly isolated"""
        # Allocate resources for multiple threads
        resources1 = self.resource_manager.allocate_thread_resources(1, "thread-1")
        resources2 = self.resource_manager.allocate_thread_resources(2, "thread-2")
        
        # Verify different proxies
        self.assertNotEqual(resources1.proxy_id, resources2.proxy_id)
        
        # Verify independent cleanup
        self.resource_manager.cleanup_thread_resources(1)
        
        # Thread 2 resources should still be active
        thread2_resources = self.resource_manager.get_thread_resources(2)
        self.assertTrue(thread2_resources.is_active)
    
    def test_resource_cleanup(self):
        """Test proper resource cleanup"""
        # Allocate and then cleanup
        resources = self.resource_manager.allocate_thread_resources(1, "test-thread")
        proxy_id = resources.proxy_id
        
        # Verify proxy is reserved
        reserved = self.resource_manager.proxy_manager.get_reserved_proxies()
        self.assertIn(proxy_id, reserved)
        
        # Cleanup
        self.resource_manager.cleanup_thread_resources(1, force=True)
        
        # Verify proxy is released
        reserved_after = self.resource_manager.proxy_manager.get_reserved_proxies()
        self.assertNotIn(proxy_id, reserved_after)


class TestThreadErrorHandler(unittest.TestCase):
    """Test error handling and recovery"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.proxy_file = os.path.join(self.temp_dir, "test_proxy.txt")
        
        with open(self.proxy_file, 'w') as f:
            f.write("proxy1:8080:user1:pass1\n")
        
        self.proxy_manager = ProxyManager(proxy_file=self.proxy_file)
        self.resource_manager = ThreadResourceManager(self.proxy_manager)
        self.error_handler = ThreadErrorHandler(self.resource_manager)
    
    def tearDown(self):
        """Clean up test environment"""
        self.resource_manager.cleanup_all_resources()
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_error_classification(self):
        """Test error classification and severity assignment"""
        # Test browser error
        browser_error = Exception("selenium webdriver error")
        should_continue = self.error_handler.handle_thread_error(1, browser_error)
        
        summary = self.error_handler.get_thread_error_summary(1)
        self.assertEqual(summary['total_errors'], 1)
        self.assertIn('browser_error', summary['error_types'])
    
    def test_error_recovery(self):
        """Test error recovery mechanisms"""
        # Allocate resources first
        self.resource_manager.allocate_thread_resources(1, "test-thread")
        
        # Simulate recoverable error
        timeout_error = Exception("timeout occurred")
        should_continue = self.error_handler.handle_thread_error(1, timeout_error)
        
        # Should attempt recovery for timeout errors
        self.assertTrue(should_continue or not should_continue)  # Either outcome is valid
    
    def test_critical_error_handling(self):
        """Test critical error handling"""
        # Simulate multiple critical errors
        for i in range(6):  # Exceed threshold
            critical_error = MemoryError("Out of memory")
            self.error_handler.handle_thread_error(i, critical_error)
        
        # Should request system shutdown
        self.assertTrue(self.error_handler.is_system_shutdown_requested())


class TestThreadingUI(unittest.TestCase):
    """Test threading user interface"""
    
    def setUp(self):
        """Set up test environment"""
        self.ui = ThreadingConfigUI()
        
        # Mock proxy manager
        self.mock_proxy_manager = Mock()
        self.mock_proxy_manager.get_available_proxies.return_value = [
            {'id': 'proxy1', 'host': 'proxy1.com', 'port': '8080'},
            {'id': 'proxy2', 'host': 'proxy2.com', 'port': '8080'},
            {'id': 'proxy3', 'host': 'proxy3.com', 'port': '8080'}
        ]
    
    @patch('builtins.input')
    def test_threading_configuration(self, mock_input):
        """Test threading configuration collection"""
        # Mock user inputs
        mock_input.side_effect = ['y', '3', '1', 'y', 'y', '']
        
        config = self.ui.get_threading_configuration(self.mock_proxy_manager)
        
        self.assertTrue(config['use_threading'])
        self.assertEqual(config['thread_count'], 3)
        self.assertEqual(config['strategy'], 'least_used')
    
    def test_progress_display(self):
        """Test progress display functionality"""
        # Create mock progress data
        progress_data = {
            1: ThreadProgress(
                thread_id=1,
                thread_name="Thread-1",
                current_step="Creating account 1/5",
                accounts_created=2,
                accounts_failed=0,
                total_accounts=5,
                status="running",
                start_time=datetime.now(),
                last_update=datetime.now(),
                current_proxy="proxy1"
            )
        }
        
        # Test display (should not raise exceptions)
        try:
            self.ui.display_thread_progress(progress_data, clear_screen=False)
        except Exception as e:
            self.fail(f"Progress display failed: {str(e)}")


class TestIntegrationScenarios(unittest.TestCase):
    """Test complete integration scenarios"""
    
    def setUp(self):
        """Set up integration test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.proxy_file = os.path.join(self.temp_dir, "test_proxy.txt")
        
        # Create multiple test proxies
        with open(self.proxy_file, 'w') as f:
            for i in range(5):
                f.write(f"proxy{i}:8080:user{i}:pass{i}\n")
        
        self.proxy_manager = ProxyManager(proxy_file=self.proxy_file)
    
    def tearDown(self):
        """Clean up integration test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_full_threading_workflow(self):
        """Test complete threading workflow"""
        # Initialize components
        resource_manager = ThreadResourceManager(self.proxy_manager)
        error_handler = ThreadErrorHandler(resource_manager)
        threaded_creator = ThreadedAccountCreator(self.proxy_manager, max_threads=3)
        
        # Mock account creator function
        def mock_account_creator(thread_id, thread_logger):
            time.sleep(0.1)  # Simulate work
            return True  # Simulate success
        
        try:
            # Start resource monitoring
            resource_manager.start_resource_monitoring()
            
            # Run threaded creation
            results = threaded_creator.create_accounts_threaded(
                total_accounts=6,
                account_creator_func=mock_account_creator,
                thread_count=3
            )
            
            # Verify results
            self.assertEqual(results['total_accounts_requested'], 6)
            self.assertEqual(results['threads_used'], 3)
            self.assertGreaterEqual(results['total_successful'], 0)
            
        finally:
            # Cleanup
            resource_manager.stop_resource_monitoring()
            resource_manager.cleanup_all_resources()
    
    def test_error_scenarios(self):
        """Test various error scenarios"""
        resource_manager = ThreadResourceManager(self.proxy_manager)
        error_handler = ThreadErrorHandler(resource_manager)
        
        # Test thread failure doesn't affect others
        def failing_account_creator(thread_id, thread_logger):
            if thread_id == 1:
                raise Exception("Simulated failure")
            time.sleep(0.1)
            return True
        
        threaded_creator = ThreadedAccountCreator(self.proxy_manager, max_threads=2)
        
        try:
            results = threaded_creator.create_accounts_threaded(
                total_accounts=4,
                account_creator_func=failing_account_creator,
                thread_count=2
            )
            
            # Should have some failures but not complete failure
            self.assertGreater(results['total_failed'], 0)
            self.assertGreaterEqual(results['total_successful'], 0)
            
        finally:
            resource_manager.cleanup_all_resources()


def run_threading_tests():
    """Run all threading tests"""
    print("🧪 Starting Multithreading Implementation Tests")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestProxyManagerThreadSafety,
        TestThreadResourceManager,
        TestThreadErrorHandler,
        TestThreadingUI,
        TestIntegrationScenarios
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("🧪 TEST SUMMARY")
    print("=" * 60)
    print(f"Tests Run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success Rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
    
    if result.errors:
        print("\n🚨 ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('\\n')[-2]}")
    
    if not result.failures and not result.errors:
        print("\n✅ All tests passed successfully!")
    
    print("=" * 60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_threading_tests()
    exit(0 if success else 1)
